'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

type SectionType = 'opportunity' | 'talent' | 'discover' | 'companies' | 'partners' | 'cta' | 'app';

interface ViewSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionType: SectionType;
  data: any;
}

export function ViewSectionModal({ isOpen, onClose, sectionType, data }: ViewSectionModalProps) {
  if (!data) return null;

  const getSectionTitle = (type: SectionType) => {
    const titles = {
      opportunity: 'Opportunity Section',
      talent: 'Talent Section',
      discover: 'Discover Section',
      companies: 'Companies Section',
      partners: 'Partners Section',
      cta: 'Call to Action Section',
      app: 'Mobile App Section'
    };
    return titles[type];
  };

  const renderBasicFields = () => (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">Heading</h4>
        <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
          {data.heading}
        </p>
      </div>
      <div>
        <h4 className="text-sm font-medium mb-2">Description</h4>
        <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
          {data.description}
        </p>
      </div>
    </div>
  );

  const renderSteps = () => {
    if (!data.steps || data.steps.length === 0) return null;
    
    return (
      <div>
        <h4 className="text-sm font-medium mb-3">Steps ({data.steps.length})</h4>
        <div className="space-y-3">
          {data.steps.map((step: any, index: number) => (
            <Card key={step._id || index} className="border-l-4 border-l-primary">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Step {index + 1}</Badge>
                    <h5 className="font-medium">{step.heading}</h5>
                  </div>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderPartners = () => {
    if (!data.partners || data.partners.length === 0) return null;
    
    return (
      <div>
        <h4 className="text-sm font-medium mb-3">Partners ({data.partners.length})</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {data.partners.map((partner: any, index: number) => (
            <Card key={partner._id || index}>
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <h5 className="font-medium">{partner.name}</h5>
                  {partner.imageURL && (
                    <div className="text-sm text-muted-foreground">
                      <span className="font-medium">Image URL:</span>
                      <p className="break-all bg-muted p-2 rounded text-xs mt-1">
                        {partner.imageURL}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderImage = () => {
    if (!data.image) return null;
    
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Image</h4>
        <div className="bg-muted p-3 rounded-md">
          <p className="text-sm text-muted-foreground break-all">{data.image}</p>
        </div>
      </div>
    );
  };

  const renderSubheading = () => {
    if (!data.subheading) return null;
    
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Subheading</h4>
        <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
          {data.subheading}
        </p>
      </div>
    );
  };

  const renderAppUrls = () => {
    if (sectionType !== 'app') return null;
    
    return (
      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">App Store URL</h4>
          <div className="bg-muted p-3 rounded-md">
            <p className="text-sm text-muted-foreground break-all">{data.appStoreURL}</p>
          </div>
        </div>
        <div>
          <h4 className="text-sm font-medium mb-2">Play Store URL</h4>
          <div className="bg-muted p-3 rounded-md">
            <p className="text-sm text-muted-foreground break-all">{data.playStoreURL}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getSectionTitle(sectionType)}
            <Badge variant="outline">{sectionType}</Badge>
          </DialogTitle>
          <DialogDescription>
            View the current content of this section
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="max-h-[70vh] pr-4">
          <div className="space-y-6">
            {renderBasicFields()}
            
            {renderSubheading()}
            
            {renderImage()}
            
            {renderAppUrls()}
            
            {data.steps && (
              <>
                <Separator />
                {renderSteps()}
              </>
            )}
            
            {data.partners && (
              <>
                <Separator />
                {renderPartners()}
              </>
            )}
            
            <Separator />
            <div className="text-xs text-muted-foreground space-y-1">
              <p><span className="font-medium">ID:</span> {data._id}</p>
              <p><span className="font-medium">Version:</span> {data.__v}</p>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
