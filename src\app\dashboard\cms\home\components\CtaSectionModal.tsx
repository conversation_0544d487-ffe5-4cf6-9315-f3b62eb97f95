'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateCtaSection } from '@/hooks/useMutation';
import { ICtaSection } from '@/types/query.types';

interface CtaSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ICtaSection;
}

interface FormData {
  heading: string;
  description: string;
  image: string;
}

export function CtaSectionModal({ isOpen, onClose, data }: CtaSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    image: ''
  });

  const { mutate: updateSection, isPending } = useUpdateCtaSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        image: data.image || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      image: formData.image.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Call to Action Section</DialogTitle>
          <DialogDescription>
            Update the CTA section content including heading, description, and image.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="heading">Heading *</Label>
                  <Input
                    id="heading"
                    value={formData.heading}
                    onChange={(e) => handleInputChange('heading', e.target.value)}
                    placeholder="Enter section heading"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter section description"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="image">Image URL</Label>
                  <Input
                    id="image"
                    value={formData.image}
                    onChange={(e) => handleInputChange('image', e.target.value)}
                    placeholder="Enter image URL"
                    type="url"
                  />
                </div>
              </div>

              {/* Preview */}
              <div className="border rounded-lg p-4 bg-red-50">
                <h4 className="text-sm font-medium mb-3 text-red-800">Preview</h4>
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-red-900">
                    {formData.heading || 'CTA Heading'}
                  </h3>
                  <p className="text-sm text-red-600">
                    {formData.description || 'CTA description will appear here...'}
                  </p>
                  {formData.image && (
                    <div className="text-xs text-red-500">
                      <span className="font-medium">Image:</span> {formData.image}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isPending || 
                !formData.heading.trim() || 
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
