'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateDiscoverSection } from '@/hooks/useMutation';
import { IDiscoverSection } from '@/types/query.types';

interface DiscoverSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IDiscoverSection;
}

interface FormData {
  heading: string;
  description: string;
  subheading: string;
}

export function DiscoverSectionModal({ isOpen, onClose, data }: DiscoverSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    subheading: ''
  });

  const { mutate: updateSection, isPending } = useUpdateDiscoverSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        subheading: data.subheading || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim() || !formData.subheading.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      subheading: formData.subheading.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Discover Section</DialogTitle>
          <DialogDescription>
            Update the discover section content including heading, subheading, and description.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="heading">Heading *</Label>
                  <Input
                    id="heading"
                    value={formData.heading}
                    onChange={(e) => handleInputChange('heading', e.target.value)}
                    placeholder="Enter section heading"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="subheading">Subheading *</Label>
                  <Input
                    id="subheading"
                    value={formData.subheading}
                    onChange={(e) => handleInputChange('subheading', e.target.value)}
                    placeholder="Enter section subheading"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter section description"
                    rows={4}
                    required
                  />
                </div>
              </div>

              {/* Preview */}
              <div className="border rounded-lg p-4 bg-purple-50">
                <h4 className="text-sm font-medium mb-3 text-purple-800">Preview</h4>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-purple-900">
                    {formData.heading || 'Section Heading'}
                  </h3>
                  <h4 className="text-md font-medium text-purple-700">
                    {formData.subheading || 'Section Subheading'}
                  </h4>
                  <p className="text-sm text-purple-600">
                    {formData.description || 'Section description will appear here...'}
                  </p>
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isPending || 
                !formData.heading.trim() || 
                !formData.description.trim() || 
                !formData.subheading.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
