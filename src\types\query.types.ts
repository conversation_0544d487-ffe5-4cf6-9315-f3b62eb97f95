import {
  UserRole,
  IAuthResponse,
  IForgetPasswordResponse,
  IRegisterResponse,
  IResetPasswordResponse
} from './common.types';

export interface IProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  address: string;
  gender: string;
  dateOfBirth: string;
  experience: string;
  qualification: string;
  careerLevel: string;
  expectedSalary: number;
  currentSalary: number;
  jobTitle: string;
  skills: string[];
  aboutMe: string;
  resume: string;
  profilePicture: string;
  socialLinks: {
    linkedin: string;
    github: string;
    portfolio: string;
  };
  cvAttachments: ICvAttachment[];
  portfolioImages: IPortfolioImage[];
}

export interface ICompany {
  companyName: string;
  companyEmail: string;
  phoneNumber: string;
  country: string;
  city: string;
  address: string;
  companySize: string;
  industry: string;
  website: string;
  aboutCompany: string;
  companyLogo: string;
  companyVideo: ICompanyVideo;
  abn: number;
}

export interface IGetJobSeekerProfileResponseDto {
  success: boolean;
  message: string;
  data: {
    profile: IProfileData;
  };
}

export interface IGetCompanyProfileResponseDto {
  success: boolean;
  message: string;
  data: {
    profile: ICompany;
  };
}

export interface IJobLocation {
  country: string;
  city: string;
  address: string;
  zipCode: string;
}

export interface IJobData {
  _id: string;
  jobTitle: string;
  jobDescription: string;
  applicationDeadline: string;
  jobCategory: string;
  jobType: string;
  jobMode: string;
  salaryType: string;
  salaryRangeStart: number;
  salaryRangeEnd: number;
  experienceLevel: string;
  qualification: string;
  careerLevel: string;
  location: IJobLocation;
  keyResponsibilities: string;
  skillsAndExperience: string;
  skillsTag: string[];
  isJobActive: boolean;
  shortlistedApplicantsCount: number;
  applicantsCount: number;
  shortlistedApplicants: number;
  totalApplicants: number;
  isBoosted: boolean;
  isDeleted: boolean;
  isPremium: boolean;
  isSaved: boolean;
  postedBy: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  premiumExpireAt?: string | null;
}

export interface IGetJobResponseDto {
  success: boolean;
  message: string;
  data: {
    job: IJobData;
  };
}

export interface ICreateJobResponseDto {
  success: boolean;
  message: string;
  data: {
    job: IJobData;
  };
}

export interface IGetJobsResponseDto {
  success: boolean;
  message: string;
  data: {
    jobs: IJobData[];
    totalJobs: number;
    page: number;
    limit: number;
  };
}

export interface IJobApplication {
  job: IJobData;
  jobSeeker: IProfileData;
  status: string;
  appliedDate: string;
  _id: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetJobApplicationsResponseDto {
  success: boolean;
  message: string;
  data: {
    applications: IJobApplication[];
    totalApplications: number;
    page: number;
    limit: number;
  };
}

export interface IGetApplicantsResponseDto {
  success: boolean;
  message: string;
  data: {
    applicants: IJobApplication[];
    totalApplicants: number;
    page: number;
    limit: number;
  };
}

export interface ISavedJob {
  _id: string;
  jobSeeker: string;
  job: IJobData;
  savedAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetSavedJobsResponseDto {
  success: boolean;
  message: string;
  data: {
    savedJobs: ISavedJob[];
    totalSavedJobs: number;
    page: number;
    limit: number;
  };
}

export interface ISavedCandidate {
  _id: string;
  recruiter: string;
  candidate: IProfileData;
  savedAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetSavedCandidatesResponseDto {
  success: boolean;
  message: string;
  data: {
    savedCandidates: ISavedCandidate[];
    totalSavedCandidates: number;
    page: number;
    limit: number;
  };
}

export interface ICompanyVideo {
  s3Key: string;
  uploadedAt: string;
  url: string;
}

export interface ICvAttachment {
  cvUrl: string;
  cvName: string;
  uploadedDate: string;
  s3Key: string;
  mimeType: string;
  fileSize: number;
  isActive: boolean;
}

export interface IPortfolioImage {
  url: string;
  s3Key: string;
  uploadedAt: string;
}

export interface IConversation {
  _id: string;
  jobApplication?: string;
  recruiterProfileId: string;
  jobSeekerProfileId: string;
  isDirectMessage: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetConversationsResponseDto {
  success: boolean;
  message: string;
  data: {
    conversations: IConversation[];
    totalConversations: number;
    page: number;
    limit: number;
  };
}

export interface IMessage {
  _id: string;
  conversationId: string;
  senderId: string;
  senderType: string;
  content: string;
  mediaFiles: {
    url: string;
    s3Key: string;
    fileName: string;
    fileSize: number;
  }[];
  read: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IGetMessagesResponseDto {
  success: boolean;
  message: string;
  data: {
    messages: IMessage[];
    totalMessages: number;
    page: number;
    limit: number;
  };
}

export interface IGetAllUsersResponseDto {
  success: boolean;
  message: string;
  data: {
    users: {
      _id: string;
      email: string;
      role: UserRole;
      isActive: boolean;
      isRestricted: boolean;
    }[];
    totalUsers: number;
    page: number;
    limit: number;
  };
}

export interface IAdminSettings {
  _id: string;
  FeaturedJobPricePerWeek: number;
  ProMemberPricePerWeek: number;
  TrialEndsAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetAdminSettingsResponseDto {
  success: boolean;
  message: string;
  data: IAdminSettings;
}

// CMS Types
export interface IOpportunityStep {
  heading: string;
  description: string;
}

export interface IOpportunitySection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
  steps: (IOpportunityStep & { _id: string })[];
}

export interface IGetOpportunitySectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IOpportunitySection;
  };
}

export interface ITalentSectionStep {
  heading: string;
  description: string;
}

export interface ITalentSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
  steps: (ITalentSectionStep & { _id: string })[];
}

export interface IGetTalentSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ITalentSection;
  };
}

export interface IDiscoverSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  subheading: string;
}

export interface IGetDiscoverSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IDiscoverSection;
  };
}

export interface ICompaniesSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  subheading: string;
}

export interface IGetCompaniesSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ICompaniesSection;
  };
}

export interface IPartner {
  name: string;
  imageURL: string;
  _id: string;
}

export interface IPartnersSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  partners: IPartner[];
}

export interface IGetPartnersSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IPartnersSection;
  };
}

export interface ICtaSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
}

export interface IGetCtaSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ICtaSection;
  };
}

export interface IAppSection {
  _id: string;
  __v: number;
  appStoreURL: string;
  description: string;
  heading: string;
  playStoreURL: string;
}

export interface IGetAppSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IAppSection;
  };
}
