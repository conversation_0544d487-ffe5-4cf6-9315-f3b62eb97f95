'use client';

import { useGetJobById, useGetAllEnums } from '@/hooks/useQuery';
import {
  // useDeleteJob,
  useToggleJobActive,
  useToggleJobPremium,
  useToggleJobDeleted
} from '@/hooks/useMutation';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import PageContainer from '@/components/layout/page-container';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format, isPast } from 'date-fns';
import { ConfirmModal } from '@/components/ui/confirm-modal';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  IconArrowLeft,
  IconMapPin,
  IconBriefcase,
  IconCalendar,
  IconCoin,
  IconSchool,
  IconUser,
  IconStar,
  // IconRocket,
  IconTrash,
  IconTrashOff
} from '@tabler/icons-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '@/components/ui/tooltip';
import { toast } from 'sonner';

export default function JobDetailPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.jobId as string;

  // Fetch job details
  const {
    data: jobData,
    isLoading,
    isError,
    refetch
  } = useGetJobById(jobId, {
    refetchOnWindowFocus: false
  });

  // Fetch enums for displaying human-readable values
  const { data: enumsData } = useGetAllEnums({
    refetchOnWindowFocus: false
  });

  const job = jobData?.data;

  // State for date picker modal
  const [isDatePickerModalOpen, setIsDatePickerModalOpen] = useState(false);
  const [selectedPremiumDate, setSelectedPremiumDate] = useState<string>('');

  // Modal state
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    action: () => void;
    confirmText: string;
    confirmVariant:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link';
  }>({
    isOpen: false,
    title: '',
    description: '',
    action: () => {},
    confirmText: 'Confirm',
    confirmVariant: 'default'
  });

  // Handle confirming premium date from date picker modal
  const handleConfirmPremiumDate = () => {
    if (!job) return;
    if (selectedPremiumDate) {
      toggleJobPremiumMutation.mutate({
        jobId,
        premiumExpireAt: new Date(selectedPremiumDate).toISOString()
      });
      setIsDatePickerModalOpen(false);
      setSelectedPremiumDate('');
    } else {
      toast.error('Please select a premium expiry date.');
    }
  };

  // Set up mutations for job status update and deletion
  const toggleJobActiveMutation = useToggleJobActive({
    onSuccess: () => {
      refetch();
    }
  });

  const toggleJobPremiumMutation = useToggleJobPremium({
    onSuccess: () => {
      refetch();
    }
  });

  const toggleJobDeletedMutation = useToggleJobDeleted({
    onSuccess: () => {
      refetch();
    }
  });

  // Handle job status toggle
  const handleToggleJobStatus = () => {
    if (!job) return;

    setConfirmModal({
      isOpen: true,
      title: job.isJobActive ? 'Deactivate Job' : 'Activate Job',
      description: job.isJobActive
        ? 'Are you sure you want to deactivate this job? It will no longer be visible to job seekers.'
        : 'Are you sure you want to activate this job? It will be visible to job seekers.',
      action: () => {
        toggleJobActiveMutation.mutate({
          jobId,
          isJobActive: !job.isJobActive
        });
      },
      confirmText: job.isJobActive ? 'Deactivate' : 'Activate',
      confirmVariant: job.isJobActive ? 'destructive' : 'default'
    });
  };

  // Handle job deletion (permanent)
  // const handleDeleteJob = () => {
  //   if (!job) return;

  //   setConfirmModal({
  //     isOpen: true,
  //     title: 'Permanently Delete Job',
  //     description:
  //       'Are you sure you want to permanently delete this job? This action cannot be undone.',
  //     action: () => {
  //       deleteJobMutation.mutate(jobId);
  //     },
  //     confirmText: 'Delete Permanently',
  //     confirmVariant: 'destructive'
  //   });
  // };

  // Handle job premium status toggle
  const handleToggleJobPremium = () => {
    if (!job) return;

    if (job.premiumExpireAt) {
      // If currently premium, confirm removal
      setConfirmModal({
        isOpen: true,
        title: 'Remove Premium Status',
        description:
          'Are you sure you want to remove the premium status from this job?',
        action: () => {
          toggleJobPremiumMutation.mutate({
            jobId,
            premiumExpireAt: null
          });
        },
        confirmText: 'Remove Premium',
        confirmVariant: 'destructive'
      });
    } else {
      // If not premium, open the date picker modal
      setSelectedPremiumDate(job.premiumExpireAt || '');
      setIsDatePickerModalOpen(true);
    }
  };

  // Handle job deleted status toggle
  const handleToggleJobDeleted = () => {
    if (!job) return;

    if (job.isDeleted === true) {
      setConfirmModal({
        isOpen: true,
        title: 'Restore Job',
        description: 'Are you sure you want to restore this job?',
        action: () => {
          toggleJobDeletedMutation.mutate({
            jobId,
            isDeleted: false
          });
        },
        confirmText: 'Restore Job',
        confirmVariant: 'default'
      });
    } else {
      setConfirmModal({
        isOpen: true,
        title: 'Mark Job as Deleted',
        description:
          'Are you sure you want to mark this job as deleted? (This is reversible)',
        action: () => {
          toggleJobDeletedMutation.mutate({
            jobId,
            isDeleted: true
          });
        },
        confirmText: 'Mark as Deleted',
        confirmVariant: 'outline'
      });
    }
  };

  // Go back to jobs list
  const goBack = () => {
    router.push('/dashboard/all-jobs');
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Get human-readable enum values
  const getEnumValue = (enumType: string, key: string) => {
    if (!enumsData?.data) return key;

    const enumObject = enumsData.data[enumType as keyof typeof enumsData.data];
    if (!enumObject) return key;

    return (enumObject as Record<string, string>)[key] || key;
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-col space-y-6'>
          <div className='flex items-center'>
            <Button variant='ghost' onClick={goBack} className='mr-4'>
              <IconArrowLeft className='mr-2 h-4 w-4' />
              Back
            </Button>
            <div className='h-8 w-64 animate-pulse rounded bg-gray-200'></div>
          </div>
          <Separator />
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <div className='h-64 animate-pulse rounded bg-gray-200'></div>
            <div className='h-64 animate-pulse rounded bg-gray-200'></div>
          </div>
        </div>
      </PageContainer>
    );
  }

  if (isError || !job) {
    return (
      <PageContainer>
        <div className='flex flex-col space-y-6'>
          <Button variant='ghost' onClick={goBack} className='w-fit'>
            <IconArrowLeft className='mr-2 h-4 w-4' />
            Back to Jobs
          </Button>
          <div className='rounded-md bg-red-50 p-4 text-red-500'>
            Failed to load job details. Please try again.
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        title={confirmModal.title}
        description={confirmModal.description}
        onConfirm={confirmModal.action}
        onClose={() => setConfirmModal({ ...confirmModal, isOpen: false })}
        confirmText={confirmModal.confirmText}
        confirmVariant={confirmModal.confirmVariant}
      />

      {/* Date Picker Modal for Premium Expiry */}
      <Dialog
        open={isDatePickerModalOpen}
        onOpenChange={setIsDatePickerModalOpen}
      >
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Set Premium Expiry Date</DialogTitle>
            <DialogDescription>
              Select a date for the premium status to expire.
            </DialogDescription>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <Input
              type='date'
              value={selectedPremiumDate}
              onChange={(e) => setSelectedPremiumDate(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsDatePickerModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmPremiumDate}
              disabled={!selectedPremiumDate}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className='flex w-full flex-col space-y-6'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <Button variant='ghost' onClick={goBack} className='mr-4'>
              <IconArrowLeft className='mr-2 h-4 w-4' />
              Back
            </Button>
            <Heading
              title={job.jobTitle}
              description={`Posted by ${job.recruiterProfile.companyName}`}
            />
          </div>
          <div className='ml-auto flex gap-2'>
            <Button
              variant={job.isJobActive ? 'destructive' : 'default'}
              onClick={handleToggleJobStatus}
            >
              {job.isJobActive ? 'Deactivate' : 'Activate'} Job
            </Button>

            <Button
              variant={!!job.premiumExpireAt ? 'default' : 'outline'}
              className={
                !!job.premiumExpireAt
                  ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                  : ''
              }
              onClick={handleToggleJobPremium}
              disabled={!job.isJobActive}
            >
              <IconStar className='mr-2 h-4 w-4' />
              {job.premiumExpireAt ? 'Remove Premium' : 'Mark Premium'}
            </Button>

            {job.isDeleted === true ? (
              <Button
                variant='outline'
                className='border-green-200 text-green-600 hover:bg-green-50'
                onClick={handleToggleJobDeleted}
              >
                <IconTrashOff className='mr-2 h-4 w-4' />
                Restore Job
              </Button>
            ) : (
              <Button
                variant='outline'
                className='border-red-200 text-red-600 hover:bg-red-50'
                onClick={handleToggleJobDeleted}
              >
                <IconTrash className='mr-2 h-4 w-4' />
                Mark as Deleted
              </Button>
            )}

            {/* <Button variant='destructive' onClick={handleDeleteJob}>
              Permanently Delete
            </Button> */}
          </div>
        </div>
        <Separator />

        <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
          {/* Main job details */}
          <div className='space-y-6 md:col-span-2'>
            <Card>
              <CardHeader>
                <CardTitle>Job Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='whitespace-pre-wrap'>{job.jobDescription}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Key Responsibilities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='whitespace-pre-wrap'>
                  {job.keyResponsibilities}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Skills & Experience</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='whitespace-pre-wrap'>
                  {job.skillsAndExperience}
                </div>
                {job.skillsTag && job.skillsTag.length > 0 && (
                  <div className='mt-4'>
                    <h4 className='mb-2 text-sm font-medium'>
                      Required Skills:
                    </h4>
                    <div className='flex flex-wrap gap-2'>
                      {job.skillsTag.map((skill, index) => (
                        <Badge key={index} variant='secondary'>
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar with job metadata */}
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Job Details</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-start'>
                  <IconBriefcase className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                  <div>
                    <p className='text-sm font-medium'>Job Type</p>
                    <p className='text-muted-foreground text-sm'>
                      {getEnumValue('JOB_TYPE_ENUM', job.jobType)} -{' '}
                      {getEnumValue('JOB_MODE_ENUM', job.jobMode)}
                    </p>
                  </div>
                </div>

                <div className='flex items-start'>
                  <IconCoin className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                  <div>
                    <p className='text-sm font-medium'>Salary</p>
                    <p className='text-muted-foreground text-sm'>
                      {getEnumValue('SALARY_TYPE_ENUM', job.salaryType)} -
                      {job.salaryRangeStart} to {job.salaryRangeEnd}
                    </p>
                  </div>
                </div>

                <div className='flex items-start'>
                  <IconUser className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                  <div>
                    <p className='text-sm font-medium'>Experience & Level</p>
                    <p className='text-muted-foreground text-sm'>
                      {getEnumValue(
                        'EXPERIENCE_RANGE_ENUM',
                        job.experienceLevel
                      )}{' '}
                      -{getEnumValue('CAREER_LEVEL_ENUM', job.careerLevel)}
                    </p>
                  </div>
                </div>

                <div className='flex items-start'>
                  <IconSchool className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                  <div>
                    <p className='text-sm font-medium'>Qualification</p>
                    <p className='text-muted-foreground text-sm'>
                      {getEnumValue('QUALIFICATION_ENUM', job.qualification)}
                    </p>
                  </div>
                </div>

                <div className='flex items-start'>
                  <IconCalendar className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                  <div>
                    <p className='text-sm font-medium'>Application Deadline</p>
                    <p className='text-muted-foreground text-sm'>
                      {formatDate(job.applicationDeadline)}
                    </p>
                  </div>
                </div>

                {job.location && job.location.formattedAddress && (
                  <div className='flex items-start'>
                    <IconMapPin className='text-muted-foreground mt-0.5 mr-2 h-5 w-5' />
                    <div>
                      <p className='text-sm font-medium'>Location</p>
                      <p className='text-muted-foreground text-sm'>
                        {job.location.formattedAddress}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Job Status</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>Status</span>
                  <Badge variant={job.isJobActive ? 'default' : 'destructive'}>
                    {job.isJobActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>Premium</span>
                  {job.premiumExpireAt ? (
                    <Badge
                      variant='outline'
                      className={`border-yellow-200 text-yellow-600 ${
                        isPast(new Date(job.premiumExpireAt))
                          ? 'bg-red-100 text-red-600'
                          : ''
                      }`}
                    >
                      Premium{' '}
                      {isPast(new Date(job.premiumExpireAt))
                        ? '(Expired)'
                        : `(Expires: ${format(
                            new Date(job.premiumExpireAt),
                            'yyyy-MM-dd'
                          )})`}
                    </Badge>
                  ) : (
                    <Badge variant='secondary'>No</Badge>
                  )}
                </div>

                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>Created</span>
                  <span className='text-muted-foreground text-sm'>
                    {formatDate(job.createdAt)}
                  </span>
                </div>

                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>Last Updated</span>
                  <span className='text-muted-foreground text-sm'>
                    {formatDate(job.updatedAt)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
