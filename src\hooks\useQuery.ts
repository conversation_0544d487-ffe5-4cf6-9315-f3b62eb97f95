import {
  useInfiniteQuery,
  useQuery,
  UseQueryOptions
} from '@tanstack/react-query';
import {
  getAllEnums,
  getAllJobs,
  getAllJobSeekers,
  getAllCompanies,
  getCompanyProfile,
  getCompanyProfileById,
  getCurrentUser,
  getJobApplicants,
  getJobById,
  getJobSeekerProfile,
  getMyJobApplications,
  getMyShortlistedApplications,
  getProfileById,
  getRecentJobs,
  getRecruitersJobs,
  getSavedCandidates,
  getSavedJobs,
  getShortlistedApplicants,
  getAllApplicants,
  getRecruiterDetailById,
  getJobSeekerProfileById,
  getAdminSettings,
  getOpportunitySection,
  getTalentSection,
  getHomeOpportunitySection,
  getDiscoverSection,
  getCompaniesSection,
  getPartnersSection,
  getCtaSection,
  getAppSection
} from '@/service/query';
import { ApiError, IAuthResponse } from '@/types/common.types';
import {
  IGetCompanyProfileResponseDto,
  IGetJobSeekerProfileResponseDto,
  ICreateJobResponseDto,
  IGetJobApplicationsResponseDto,
  IGetSavedCandidatesResponseDto,
  IGetSavedJobsResponseDto,
  IGetApplicantsResponseDto,
  IGetAllUsersResponseDto,
  IGetAdminSettingsResponseDto,
  IGetOpportunitySectionResponseDto,
  IGetTalentSectionResponseDto,
  IGetDiscoverSectionResponseDto,
  IGetCompaniesSectionResponseDto,
  IGetPartnersSectionResponseDto,
  IGetCtaSectionResponseDto,
  IGetAppSectionResponseDto
} from '@/types/query.types';

export function useGetCurrentUser(
  options?: Omit<
    UseQueryOptions<IAuthResponse, ApiError, IAuthResponse>,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IAuthResponse, ApiError>({
    queryKey: ['get-current-user'],
    queryFn: getCurrentUser,
    ...options
  });
}

export function useGetJobSeekerProfile(
  options?: Omit<
    UseQueryOptions<
      IGetJobSeekerProfileResponseDto,
      ApiError,
      IGetJobSeekerProfileResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetJobSeekerProfileResponseDto, ApiError>({
    queryKey: ['get-jobseeker-profile'],
    queryFn: getJobSeekerProfile,
    ...options
  });
}
export function useGetCompanyProfile(
  options?: Omit<
    UseQueryOptions<
      IGetCompanyProfileResponseDto,
      ApiError,
      IGetCompanyProfileResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetCompanyProfileResponseDto, ApiError>({
    queryKey: ['get-company-profile'],
    queryFn: getCompanyProfile,
    ...options
  });
}

export function useGetAllEnums(
  options?: Omit<
    UseQueryOptions<any, ApiError>, // Changed to any
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-all-enums'],
    queryFn: getAllEnums,
    ...options
  });
}

export function useGetJobById(
  id: string,
  options?: Omit<
    UseQueryOptions<ICreateJobResponseDto, ApiError>,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<ICreateJobResponseDto, ApiError>({
    queryKey: ['get-job-by-id', id],
    queryFn: () => getJobById(id),
    ...options
  });
}

export function useGetProfileById(
  id: string,
  options?: Omit<
    UseQueryOptions<any, ApiError>, // Changed to any
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-profile-by-id', id],
    queryFn: () => getProfileById(id),
    ...options
  });
}

export function useGetRecruiterJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      any, // Changed to any
      ApiError,
      any // Changed to any
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-recruiter-jobs', params],
    queryFn: () => getRecruitersJobs(params),
    ...options
  });
}

export function useGetRecentJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      any, // Changed to any
      ApiError,
      any // Changed to any
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-recent-jobs', params],
    queryFn: () => getRecentJobs(params),
    ...options
  });
}

export function useGetAllJobs(
  params?: any, // Changed to any
  options?: Omit<
    UseQueryOptions<any, ApiError, any>, // Changed to any
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-all-jobs', params],
    queryFn: () => getAllJobs(params),
    ...options
  });
}

export function useGetAllJobSeekers(
  params?: any, // Changed to any
  options?: Omit<
    UseQueryOptions<
      IGetAllUsersResponseDto, // Changed to IGetAllUsersResponseDto
      ApiError,
      IGetAllUsersResponseDto // Changed to IGetAllUsersResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetAllUsersResponseDto, ApiError>({
    // Changed to IGetAllUsersResponseDto
    queryKey: ['get-all-jobseekers', params],
    queryFn: () => getAllJobSeekers(params),
    ...options
  });
}

export function useGetMyJobApplications(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      IGetJobApplicationsResponseDto,
      ApiError,
      IGetJobApplicationsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetJobApplicationsResponseDto, ApiError>({
    queryKey: ['get-my-job-applications', params],
    queryFn: () => getMyJobApplications(params),
    ...options
  });
}

export function useGetMyShortlistedApplications(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      IGetJobApplicationsResponseDto,
      ApiError,
      IGetJobApplicationsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetJobApplicationsResponseDto, ApiError>({
    queryKey: ['get-my-shortlisted-applications', params],
    queryFn: () => getMyShortlistedApplications(params),
    ...options
  });
}

export function useGetSavedCandidates(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      IGetSavedCandidatesResponseDto,
      ApiError,
      IGetSavedCandidatesResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetSavedCandidatesResponseDto, ApiError>({
    queryKey: ['get-saved-candidates'],
    queryFn: () => getSavedCandidates(params),
    ...options
  });
}

export function useGetJobApplicants(
  jobId: string,
  options?: Omit<
    UseQueryOptions<
      IGetApplicantsResponseDto, // Changed to IGetApplicantsResponseDto
      ApiError,
      IGetApplicantsResponseDto // Changed to IGetApplicantsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetApplicantsResponseDto, ApiError>({
    // Changed to IGetApplicantsResponseDto
    queryKey: ['get-job-applicants', jobId],
    queryFn: () => getJobApplicants(jobId),
    // enabled: !!jobId,
    ...options
  });
}

export function useGetAllCompanies(
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    coordinates?: [number, number];
    maxDistance?: string;
  },
  options?: Omit<
    UseQueryOptions<
      IGetCompanyProfileResponseDto, // Changed to IGetCompanyProfileResponseDto
      ApiError,
      IGetCompanyProfileResponseDto // Changed to IGetCompanyProfileResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetCompanyProfileResponseDto, ApiError>({
    // Changed to IGetCompanyProfileResponseDto
    queryKey: ['get-all-companies', params],
    queryFn: () => getAllCompanies(params),
    ...options
  });
}
export function useGetSavedJobs(
  params?: {
    page?: number;
    limit?: number;
  },
  options?: Omit<
    UseQueryOptions<
      IGetSavedJobsResponseDto,
      ApiError,
      IGetSavedJobsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetSavedJobsResponseDto, ApiError>({
    queryKey: ['get-saved-jobs', params],
    queryFn: () => getSavedJobs(params),
    ...options
  });
}

export function useGetCompanyProfileById(
  id: string,
  options?: Omit<
    UseQueryOptions<
      IGetCompanyProfileResponseDto,
      ApiError,
      IGetCompanyProfileResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetCompanyProfileResponseDto, ApiError>({
    queryKey: ['get-company-profile-by-id', id],
    queryFn: () => getCompanyProfileById(id),
    enabled: !!id,
    ...options
  });
}

export function useGetRecruiterDetailById(
  id: string,
  options?: Omit<
    UseQueryOptions<
      any, // Changed to any
      ApiError,
      any // Changed to any
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-recruiter-detail-by-id', id],
    queryFn: () => getRecruiterDetailById(id),
    enabled: !!id,
    ...options
  });
}

export function useGetJobSeekerProfileById(
  id: string,
  options?: Omit<
    UseQueryOptions<
      any, // Changed to any
      ApiError,
      any // Changed to any
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<any, ApiError>({
    // Changed to any
    queryKey: ['get-jobseeker-profile-by-id', id],
    queryFn: () => getJobSeekerProfileById(id),
    enabled: !!id,
    ...options
  });
}

export function useGetShortlistedApplicants(
  params?: any, // Changed to any
  options?: Omit<
    UseQueryOptions<
      IGetApplicantsResponseDto, // Changed to IGetApplicantsResponseDto
      ApiError,
      IGetApplicantsResponseDto // Changed to IGetApplicantsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetApplicantsResponseDto, ApiError>({
    // Changed to IGetApplicantsResponseDto
    queryKey: ['get-shortlisted-applicants', params],
    queryFn: () => getShortlistedApplicants(params),
    ...options
  });
}

export function useGetAllApplicants(
  params?: any, // Changed to any
  options?: Omit<
    UseQueryOptions<
      IGetApplicantsResponseDto, // Changed to IGetApplicantsResponseDto
      ApiError,
      IGetApplicantsResponseDto // Changed to IGetApplicantsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetApplicantsResponseDto, ApiError>({
    // Changed to IGetApplicantsResponseDto
    queryKey: ['get-all-applicants', params],
    queryFn: () => getAllApplicants(params),
    ...options
  });
}

export function useGetAdminSettings(
  options?: Omit<
    UseQueryOptions<
      IGetAdminSettingsResponseDto,
      ApiError,
      IGetAdminSettingsResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetAdminSettingsResponseDto, ApiError>({
    queryKey: ['get-admin-settings'],
    queryFn: getAdminSettings,
    ...options
  });
}

export function useGetOpportunitySection(
  options?: Omit<
    UseQueryOptions<
      IGetOpportunitySectionResponseDto,
      ApiError,
      IGetOpportunitySectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetOpportunitySectionResponseDto, ApiError>({
    queryKey: ['get-opportunity-section'],
    queryFn: getOpportunitySection,
    ...options
  });
}

export function useGetTalentSection(
  options?: Omit<
    UseQueryOptions<
      IGetTalentSectionResponseDto,
      ApiError,
      IGetTalentSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetTalentSectionResponseDto, ApiError>({
    queryKey: ['get-talent-section'],
    queryFn: getTalentSection,
    ...options
  });
}

export function useGetHomeOpportunitySection(
  options?: Omit<
    UseQueryOptions<
      IGetOpportunitySectionResponseDto, // Changed to IGetOpportunitySectionResponseDto
      ApiError,
      IGetOpportunitySectionResponseDto // Changed to IGetOpportunitySectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetOpportunitySectionResponseDto, ApiError>({
    // Changed to IGetOpportunitySectionResponseDto
    queryKey: ['get-home-opportunity-section'],
    queryFn: getHomeOpportunitySection,
    ...options
  });
}

export function useGetDiscoverSection(
  options?: Omit<
    UseQueryOptions<
      IGetDiscoverSectionResponseDto,
      ApiError,
      IGetDiscoverSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetDiscoverSectionResponseDto, ApiError>({
    queryKey: ['get-discover-section'],
    queryFn: getDiscoverSection,
    ...options
  });
}

export function useGetCompaniesSection(
  options?: Omit<
    UseQueryOptions<
      IGetCompaniesSectionResponseDto,
      ApiError,
      IGetCompaniesSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetCompaniesSectionResponseDto, ApiError>({
    queryKey: ['get-companies-section'],
    queryFn: getCompaniesSection,
    ...options
  });
}

export function useGetPartnersSection(
  options?: Omit<
    UseQueryOptions<
      IGetPartnersSectionResponseDto,
      ApiError,
      IGetPartnersSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetPartnersSectionResponseDto, ApiError>({
    queryKey: ['get-partners-section'],
    queryFn: getPartnersSection,
    ...options
  });
}

export function useGetCtaSection(
  options?: Omit<
    UseQueryOptions<
      IGetCtaSectionResponseDto,
      ApiError,
      IGetCtaSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetCtaSectionResponseDto, ApiError>({
    queryKey: ['get-cta-section'],
    queryFn: getCtaSection,
    ...options
  });
}

export function useGetAppSection(
  options?: Omit<
    UseQueryOptions<
      IGetAppSectionResponseDto,
      ApiError,
      IGetAppSectionResponseDto
    >,
    'queryKey' | 'queryFn'
  >
) {
  return useQuery<IGetAppSectionResponseDto, ApiError>({
    queryKey: ['get-app-section'],
    queryFn: getAppSection,
    ...options
  });
}
