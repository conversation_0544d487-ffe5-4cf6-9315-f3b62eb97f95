'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateAppSection } from '@/hooks/useMutation';
import { IAppSection } from '@/types/query.types';

interface AppSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IAppSection;
}

interface FormData {
  heading: string;
  description: string;
  appStoreURL: string;
  playStoreURL: string;
}

export function AppSectionModal({ isOpen, onClose, data }: AppSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    appStoreURL: '',
    playStoreURL: ''
  });

  const { mutate: updateSection, isPending } = useUpdateAppSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        appStoreURL: data.appStoreURL || '',
        playStoreURL: data.playStoreURL || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      appStoreURL: formData.appStoreURL.trim(),
      playStoreURL: formData.playStoreURL.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Mobile App Section</DialogTitle>
          <DialogDescription>
            Update the mobile app section content including heading, description, and store URLs.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="heading">Heading *</Label>
                  <Input
                    id="heading"
                    value={formData.heading}
                    onChange={(e) => handleInputChange('heading', e.target.value)}
                    placeholder="Enter section heading"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter section description"
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="appStoreURL">App Store URL</Label>
                  <Input
                    id="appStoreURL"
                    value={formData.appStoreURL}
                    onChange={(e) => handleInputChange('appStoreURL', e.target.value)}
                    placeholder="Enter App Store URL"
                    type="url"
                  />
                </div>

                <div>
                  <Label htmlFor="playStoreURL">Play Store URL</Label>
                  <Input
                    id="playStoreURL"
                    value={formData.playStoreURL}
                    onChange={(e) => handleInputChange('playStoreURL', e.target.value)}
                    placeholder="Enter Play Store URL"
                    type="url"
                  />
                </div>
              </div>

              {/* Preview */}
              <div className="border rounded-lg p-4 bg-indigo-50">
                <h4 className="text-sm font-medium mb-3 text-indigo-800">Preview</h4>
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-indigo-900">
                    {formData.heading || 'App Section Heading'}
                  </h3>
                  <p className="text-sm text-indigo-600">
                    {formData.description || 'App section description will appear here...'}
                  </p>
                  <div className="space-y-2">
                    {formData.appStoreURL && (
                      <div className="text-xs text-indigo-500">
                        <span className="font-medium">App Store:</span> {formData.appStoreURL}
                      </div>
                    )}
                    {formData.playStoreURL && (
                      <div className="text-xs text-indigo-500">
                        <span className="font-medium">Play Store:</span> {formData.playStoreURL}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isPending || 
                !formData.heading.trim() || 
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
